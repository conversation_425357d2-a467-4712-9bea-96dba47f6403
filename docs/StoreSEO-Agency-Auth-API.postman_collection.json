{"info": {"name": "StoreSEO Agency Auth API", "description": "Comprehensive API collection for testing the StoreSEO Agency authentication system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8001", "type": "string"}, {"key": "verificationToken", "value": "", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}], "item": [{"name": "Registration", "item": [{"name": "Valid Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "register"]}}, "response": []}, {"name": "Duplicate Email Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"AnotherPass123\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "register"]}}, "response": []}, {"name": "Invalid Email Format", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Invalid\",\n  \"lastName\": \"User\",\n  \"email\": \"invalid-email\",\n  \"password\": \"SecurePass123\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "register"]}}, "response": []}, {"name": "Weak Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Weak\",\n  \"lastName\": \"Password\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "register"]}}, "response": []}]}, {"name": "Email Verification", "item": [{"name": "Valid Token Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"{{verificationToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "verify-email"]}}, "response": []}, {"name": "Invalid <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"invalid.jwt.token\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "verify-email"]}}, "response": []}, {"name": "Expired Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.expired\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "verify-email"]}}, "response": []}]}, {"name": "Workspace Setup", "item": [{"name": "Valid Workspace Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{verificationToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"workspaceName\": \"My SEO Agency\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/setup-workspace", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "setup-workspace"]}}, "response": []}, {"name": "Invalid Workspace Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{verificationToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"workspaceName\": \"A\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/setup-workspace", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "setup-workspace"]}}, "response": []}, {"name": "Unauthorized Workspace Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"workspaceName\": \"Unauthorized Workspace\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/setup-workspace", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "setup-workspace"]}}, "response": []}]}, {"name": "Token Management", "item": [{"name": "Refresh Access Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "refresh-token"]}}, "response": []}, {"name": "Invalid Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"invalid.refresh.token\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "refresh-token"]}}, "response": []}, {"name": "Validate Access Token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/v1/auth/validate", "host": ["{{baseUrl}}"], "path": ["v1", "auth", "validate"]}}, "response": []}]}, {"name": "Protected Routes", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/v1/user/profile", "host": ["{{baseUrl}}"], "path": ["v1", "user", "profile"]}}, "response": []}, {"name": "Get Workspace Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/v1/workspace", "host": ["{{baseUrl}}"], "path": ["v1", "workspace"]}}, "response": []}]}]}