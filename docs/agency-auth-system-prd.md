# StoreSEO Agency Authentication System - Product Requirements Document

## 1. Executive Summary

This PRD outlines the implementation of a comprehensive JWT-based authentication system for the StoreSEO Agency platform, featuring user registration, email verification, workspace setup, and role-based access control.

## 2. Current State Analysis

### Existing Infrastructure

- **Database**: `agency_users` table with fields: `firstName`, `lastName`, `email`, `password`, `role`, `status`
- **Backend**: Agency server with JWT authentication using Passport.js
- **Frontend**: React Router v7 with TypeScript
- **Email System**: Queue-based email processing with Handlebars templates
- **Roles**: OWNER, ADMIN, MANAGER, EDITOR, VIEWER with hierarchical permissions

### Existing Components to Leverage

- JWT configuration in `agency/server/config/passport.ts`
- User role scopes in `agency/server/config/userRoleScopes.ts`
- Email infrastructure in `web/api/queue/jobs/email/`
- Token types: ACCESS, REFRESH, VERIFY_EMAIL

## 3. System Architecture

### 3.1 Authentication Flow Overview

```
Registration → Email Verification → Workspace Setup → Dashboard Access
     ↓              ↓                    ↓              ↓
  JWT Token    Verification Link    Access Token    Authenticated
  (3 days)     (Magic Link)         (Session)       Session
```

### 3.2 Database Schema Requirements

#### 3.2.1 Agency Users Table Updates

```sql
-- Existing fields: id, firstName, lastName, email, password, role, status, created_at, updated_at
-- Add new fields:
ALTER TABLE agency_users ADD COLUMN workspace_id INTEGER;
ALTER TABLE agency_users ADD COLUMN email_verified_at TIMESTAMP;
ALTER TABLE agency_users ADD COLUMN verification_token_expires_at TIMESTAMP;
```

#### 3.2.2 New Workspaces Table

```sql
CREATE TABLE agency_workspaces (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  owner_id INTEGER REFERENCES agency_users(id),
  settings JSON DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3.2.3 User Status Enum

```typescript
enum AgencyUserStatus {
  VERIFICATION_PENDING = "VERIFICATION_PENDING",
  VERIFIED = "VERIFIED",
  SUSPENDED = "SUSPENDED",
  DELETED = "DELETED",
}
```

## 4. API Specifications

### 4.1 Registration Endpoint

**POST** `/v1/auth/register`

**Request Body:**

```json
{
  "firstName": "string (required, 2-50 chars)",
  "lastName": "string (required, 2-50 chars)",
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars, 1 uppercase, 1 lowercase, 1 number)"
}
```

**Success Response (201):**

```json
{
  "success": true,
  "message": "Registration successful. Please check your email for verification.",
  "data": {
    "userId": "integer",
    "email": "string",
    "verificationSent": true
  }
}
```

**Error Responses:**

- `400`: Validation errors
- `409`: Email already exists
- `500`: Server error

### 4.2 Email Verification Endpoint

**POST** `/v1/auth/verify-email`

**Request Body:**

```json
{
  "token": "string (required, JWT verification token)"
}
```

**Success Response (200):**

```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "userId": "integer",
    "email": "string",
    "requiresWorkspaceSetup": true
  }
}
```

### 4.3 Workspace Setup Endpoint

**POST** `/v1/auth/setup-workspace`

**Headers:**

```
Authorization: Bearer <verification_token>
```

**Request Body:**

```json
{
  "workspaceName": "string (required, 3-50 chars, alphanumeric + spaces)"
}
```

**Success Response (201):**

```json
{
  "success": true,
  "message": "Workspace created successfully",
  "data": {
    "user": {
      "id": "integer",
      "firstName": "string",
      "lastName": "string",
      "email": "string",
      "role": "OWNER",
      "status": "VERIFIED"
    },
    "workspace": {
      "id": "integer",
      "name": "string",
      "slug": "string"
    },
    "tokens": {
      "access": {
        "token": "string",
        "expires": "ISO date string"
      },
      "refresh": {
        "token": "string",
        "expires": "ISO date string"
      }
    }
  }
}
```

## 5. Frontend Specifications

### 5.1 Registration Page (`/auth/register`)

**Location:** `agency/frontend/app/routes/auth/register.tsx`

**Features:**

- Form with firstName, lastName, email, password fields
- Real-time validation with error display
- Password strength indicator
- Loading states during submission
- Success message with email check instruction

**Validation Rules:**

- firstName/lastName: 2-50 characters, letters only
- Email: Valid email format, not already registered
- Password: Min 8 chars, 1 uppercase, 1 lowercase, 1 number

### 5.2 Email Verification Page (`/auth/verify`)

**Location:** `agency/frontend/app/routes/auth/verify.tsx`

**Features:**

- Extract token from URL query parameter
- Automatic verification on page load
- Loading spinner during verification
- Success/error state display
- Redirect to workspace setup on success

### 5.3 Workspace Setup Page (`/auth/setup-workspace`)

**Location:** `agency/frontend/app/routes/auth/setup-workspace.tsx`

**Features:**

- Workspace name input field
- Real-time slug generation preview
- Validation and error handling
- Submit button with loading state
- Redirect to dashboard on completion

## 6. Email Templates

### 6.1 Verification Email Template

**Subject:** "Verify your StoreSEO Agency account"

**Template Location:** `web/api/templates/agency-email-verification.hbs`

**Variables:**

- `{{firstName}}` - User's first name
- `{{verificationLink}}` - Magic link with JWT token
- `{{STORESEO_LOGO}}` - Logo URL
- `{{emailProvider}}` - Email provider identifier

**Content Structure:**

- Welcome message with user's name
- Clear call-to-action button
- Link expiration notice (3 days)
- Support contact information
- Unsubscribe footer

## 7. Security Considerations

### 7.1 Token Management

- **Verification Token**: 3-day expiration, single-use
- **Access Token**: 5-minute expiration for high security
- **Refresh Token**: 7-day expiration, stored securely
- **Token Rotation**: New refresh token on each access token refresh

### 7.2 Password Security

- Bcrypt hashing with salt rounds: 12
- Password complexity requirements enforced
- No password storage in logs or client-side

### 7.3 Rate Limiting

- Registration: 5 attempts per IP per hour
- Email verification: 3 attempts per token
- Login attempts: 5 per email per 15 minutes

## 8. Error Handling

### 8.1 Backend Error Responses

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": "Additional context (dev mode only)"
  }
}
```

### 8.2 Frontend Error Display

- Form validation errors inline
- API errors in toast notifications
- Network errors with retry options
- Graceful fallbacks for all error states

## 9. Testing Strategy

### 9.1 Backend Testing

- Unit tests for all service methods
- Integration tests for API endpoints
- Email delivery testing with mock providers
- JWT token validation testing

### 9.2 Frontend Testing

- Component unit tests with React Testing Library
- Form validation testing
- Navigation flow testing
- Error state testing

### 9.3 End-to-End Testing

- Complete registration flow
- Email verification process
- Workspace setup completion
- Authentication state persistence

## 10. Implementation Phases

### Phase 1: Backend Foundation (Week 1)

- Database schema updates
- User registration endpoint
- Email verification system
- JWT token management

### Phase 2: Frontend Core (Week 2)

- Registration page implementation
- Email verification page
- Workspace setup page
- Authentication context

### Phase 3: Integration & Testing (Week 3)

- End-to-end flow testing
- Error handling refinement
- Security audit
- Performance optimization

### Phase 4: Deployment & Monitoring (Week 4)

- Production deployment
- Monitoring setup
- User feedback collection
- Bug fixes and improvements

## 11. Success Metrics

- Registration completion rate > 85%
- Email verification rate > 70%
- Workspace setup completion > 90%
- Authentication error rate < 2%
- Page load times < 2 seconds

## 12. Dependencies

- Existing JWT infrastructure
- Email queue system
- React Router v7 setup
- Database migration system
- Existing user role permissions

## 13. Risks & Mitigation

### High Risk

- **Email Deliverability**: Use existing proven email infrastructure
- **Security Vulnerabilities**: Comprehensive security audit

### Medium Risk

- **User Experience**: Extensive user testing and feedback
- **Performance**: Load testing and optimization

### Low Risk

- **Integration Issues**: Leverage existing patterns and infrastructure

## 14. Detailed Technical Specifications

### 14.1 JWT Token Payloads

#### Verification Token

```typescript
interface VerificationTokenPayload {
  sub: string; // User ID
  email: string; // User email
  link_type: "verification";
  iat: number; // Issued at
  exp: number; // Expires at (3 days)
}
```

#### Access Token

```typescript
interface AccessTokenPayload {
  sub: string; // User ID
  email: string; // User email
  name: string; // Full name
  role: string; // User role
  workspace_id: number; // Workspace ID
  scopes: string[]; // Permission scopes
  type: "access";
  iat: number; // Issued at
  exp: number; // Expires at (5 minutes)
}
```

### 14.2 Database Migrations

#### Migration 1: Update Agency Users Table

```sql
-- File: web/sequelize/migrations/YYYYMMDD-update-agency-users-for-auth.js
ALTER TABLE agency_users
ADD COLUMN workspace_id INTEGER,
ADD COLUMN email_verified_at TIMESTAMP,
ADD COLUMN verification_token_expires_at TIMESTAMP,
ADD CONSTRAINT fk_agency_users_workspace
  FOREIGN KEY (workspace_id) REFERENCES agency_workspaces(id);

-- Update existing users to VERIFIED status
UPDATE agency_users SET status = 'VERIFIED' WHERE status IS NULL;
```

#### Migration 2: Create Workspaces Table

```sql
-- File: web/sequelize/migrations/YYYYMMDD-create-agency-workspaces.js
CREATE TABLE agency_workspaces (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  owner_id INTEGER NOT NULL REFERENCES agency_users(id),
  settings JSON DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_agency_workspaces_owner_id ON agency_workspaces(owner_id);
CREATE INDEX idx_agency_workspaces_slug ON agency_workspaces(slug);
```

### 14.3 Service Layer Architecture

#### AuthService Methods

```typescript
class AuthService {
  async registerUser(userData: RegisterUserData): Promise<User>;
  async verifyEmail(token: string): Promise<VerificationResult>;
  async setupWorkspace(userId: number, workspaceName: string): Promise<WorkspaceSetupResult>;
  async generateVerificationToken(user: User): Promise<string>;
  async generateAccessTokens(user: User): Promise<TokenPair>;
  async refreshTokens(refreshToken: string): Promise<TokenPair>;
}
```

#### WorkspaceService Methods

```typescript
class WorkspaceService {
  async createWorkspace(name: string, ownerId: number): Promise<Workspace>;
  async generateSlug(name: string): Promise<string>;
  async validateWorkspaceName(name: string): Promise<boolean>;
}
```

### 14.4 Frontend Route Protection

#### Authentication Context

```typescript
interface AuthContextType {
  user: User | null;
  workspace: Workspace | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (tokens: TokenPair) => void;
  logout: () => void;
  refreshToken: () => Promise<void>;
}
```

#### Protected Route Component

```typescript
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) return <LoadingSpinner />;
  if (!isAuthenticated) return <Navigate to="/auth/register" />;

  return <>{children}</>;
}
```

## 15. API Error Codes

### Authentication Errors

- `AUTH_001`: Invalid credentials
- `AUTH_002`: Email already exists
- `AUTH_003`: Invalid verification token
- `AUTH_004`: Verification token expired
- `AUTH_005`: User not found
- `AUTH_006`: Account not verified
- `AUTH_007`: Invalid refresh token

### Validation Errors

- `VAL_001`: Invalid email format
- `VAL_002`: Password too weak
- `VAL_003`: Required field missing
- `VAL_004`: Field length invalid
- `VAL_005`: Invalid workspace name

### System Errors

- `SYS_001`: Database connection error
- `SYS_002`: Email service unavailable
- `SYS_003`: Token generation failed
- `SYS_004`: Workspace creation failed

## 16. Monitoring & Analytics

### Key Metrics to Track

- Registration funnel conversion rates
- Email verification success rates
- Workspace setup completion rates
- Authentication error frequencies
- Token refresh patterns
- User session durations

### Logging Requirements

- All authentication attempts (success/failure)
- Email verification events
- Workspace creation events
- Token generation and validation
- Security-related events (suspicious activity)

### Alerting Thresholds

- Registration failure rate > 10%
- Email verification rate < 60%
- Authentication errors > 5% of requests
- Token validation failures > 2%

## 17. Postman Collection Examples

### Collection Structure

```
StoreSEO Agency Auth API/
├── Registration/
│   ├── Valid Registration
│   ├── Duplicate Email
│   └── Invalid Data
├── Email Verification/
│   ├── Valid Token
│   ├── Expired Token
│   └── Invalid Token
├── Workspace Setup/
│   ├── Valid Setup
│   ├── Invalid Name
│   └── Unauthorized
└── Token Management/
    ├── Refresh Token
    ├── Invalid Refresh
    └── Token Validation
```

### Sample Request: User Registration

```json
{
  "name": "Valid User Registration",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "body": {
      "mode": "raw",
      "raw": "{\n  \"firstName\": \"John\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123\"\n}"
    },
    "url": {
      "raw": "{{baseUrl}}/v1/auth/register",
      "host": ["{{baseUrl}}"],
      "path": ["v1", "auth", "register"]
    }
  }
}
```

## 18. Deployment Checklist

### Pre-Deployment

- [ ] Database migrations tested
- [ ] Environment variables configured
- [ ] Email templates deployed
- [ ] JWT secrets generated
- [ ] Rate limiting configured
- [ ] SSL certificates verified

### Post-Deployment

- [ ] Health checks passing
- [ ] Email delivery tested
- [ ] Authentication flow verified
- [ ] Error monitoring active
- [ ] Performance metrics baseline
- [ ] Security scan completed

### Rollback Plan

- [ ] Database rollback scripts ready
- [ ] Previous version deployment ready
- [ ] User communication plan
- [ ] Data integrity verification steps

## 19. Future Enhancements

### Phase 2 Features

- Social login integration (Google, GitHub)
- Two-factor authentication (2FA)
- Single Sign-On (SSO) support
- Advanced user management
- Audit logging dashboard

### Phase 3 Features

- Multi-workspace support per user
- Team invitation system
- Advanced role customization
- API key management
- Webhook notifications

## 20. Conclusion

This PRD provides a comprehensive blueprint for implementing a secure, scalable JWT-based authentication system for the StoreSEO Agency platform. The system leverages existing infrastructure while introducing modern authentication patterns and security best practices.

The phased implementation approach ensures minimal disruption to existing services while delivering a robust authentication experience that can scale with the platform's growth.
