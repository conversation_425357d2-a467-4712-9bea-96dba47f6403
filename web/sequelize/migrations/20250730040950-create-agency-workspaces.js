"use strict";

const attributes = require("../attributes/agency-workspace-attributes");
const { AGENCY_WORKSPACE, AGENCY_USER } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        AGENCY_WORKSPACE,
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          ...attributes,
        },
        { transaction }
      );

      // Add indexes for better query performance
      await queryInterface.addIndex(
        AGENCY_WORKSPACE,
        ["owner_id"],
        {
          name: "idx_agency_workspaces_owner_id",
          transaction,
        }
      );

      await queryInterface.addIndex(
        AGENCY_WORKSPACE,
        ["slug"],
        {
          name: "idx_agency_workspaces_slug",
          unique: true,
          transaction,
        }
      );

      // Add foreign key constraint
      await queryInterface.addConstraint(AGENCY_WORKSPACE, {
        fields: ["owner_id"],
        type: "foreign key",
        name: "fk_agency_workspaces_owner_id",
        references: {
          table: AGENCY_USER,
          field: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        transaction,
      });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Remove foreign key constraint first
      await queryInterface.removeConstraint(
        AGENCY_WORKSPACE,
        "fk_agency_workspaces_owner_id",
        { transaction }
      );

      // Remove indexes
      await queryInterface.removeIndex(
        AGENCY_WORKSPACE,
        "idx_agency_workspaces_slug",
        { transaction }
      );

      await queryInterface.removeIndex(
        AGENCY_WORKSPACE,
        "idx_agency_workspaces_owner_id",
        { transaction }
      );

      // Drop table
      await queryInterface.dropTable(AGENCY_WORKSPACE, { transaction });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
