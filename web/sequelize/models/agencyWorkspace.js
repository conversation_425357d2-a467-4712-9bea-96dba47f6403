"use strict";
const { Model } = require("sequelize");
const attributes = require("../attributes/agency-workspace-attributes");
const { timestamps } = require("../config/common-options");
const { AGENCY_WORKSPACE } = require("../config/table-names");

module.exports = (sequelize, DataTypes) => {
  class AgencyWorkspace extends Model {
    static associate(models) {
      // AgencyWorkspace belongs to AgencyUser (owner)
      AgencyWorkspace.belongsTo(models.AgencyUser, {
        foreignKey: 'owner_id',
        as: 'owner',
      });

      // AgencyWorkspace has many AgencyUsers (members)
      AgencyWorkspace.hasMany(models.AgencyUser, {
        foreignKey: 'workspace_id',
        as: 'members',
      });
    }

    // Helper methods
    get memberCount() {
      return this.members ? this.members.length : 0;
    }

    toPublicJSON() {
      return {
        id: this.id,
        name: this.name,
        slug: this.slug,
        created_at: this.created_at,
        updated_at: this.updated_at,
      };
    }
  }
  
  AgencyWorkspace.init(attributes, {
    sequelize,
    modelName: "AgencyWorkspace",
    tableName: AGENCY_WORKSPACE,
    ...timestamps,
  });
  
  return AgencyWorkspace;
};
