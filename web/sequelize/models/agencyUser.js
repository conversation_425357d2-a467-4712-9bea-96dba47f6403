"use strict";
const { Model } = require("sequelize");
const attributes = require("../attributes/agency-user-attribues");
const { timestamps } = require("../config/common-options");
const { AGENCY_USER } = require("../config/table-names");

module.exports = (sequelize, DataTypes) => {
  class AgencyUser extends Model {
    static associate(models) {
      // AgencyUser belongs to AgencyWorkspace
      AgencyUser.belongsTo(models.AgencyWorkspace, {
        foreignKey: "workspace_id",
        as: "workspace",
      });
    }

    // Helper methods
    get fullName() {
      return `${this.firstName} ${this.lastName}`;
    }

    get isVerified() {
      return this.email_verified_at !== null;
    }

    get isVerificationExpired() {
      if (!this.verification_token_expires_at) return false;
      return new Date() > this.verification_token_expires_at;
    }
  }
  AgencyUser.init(attributes, {
    sequelize,
    modelName: "AgencyUser",
    tableName: AGENCY_USER,
    ...timestamps,
  });
  return AgencyUser;
};
