const { DataTypes } = require("sequelize");
const { timestampFields } = require("../config/common-options");

/**
 * @type {import("sequelize").ModelAttributes}
 */
const attributes = {
  name: { 
    type: DataTypes.STRING(255), 
    allowNull: false,
    validate: {
      len: [3, 50],
      notEmpty: true
    }
  },
  slug: { 
    type: DataTypes.STRING(255), 
    allowNull: false, 
    unique: true,
    validate: {
      is: /^[a-z0-9-]+$/i, // alphanumeric and hyphens only
      len: [3, 50],
      notEmpty: true
    }
  },
  owner_id: { 
    type: DataTypes.INTEGER, 
    allowNull: false,
    references: {
      model: 'agency_users',
      key: 'id'
    }
  },
  settings: { 
    type: DataTypes.JSON, 
    allowNull: false,
    defaultValue: {}
  },
  ...timestampFields,
};

module.exports = attributes;
