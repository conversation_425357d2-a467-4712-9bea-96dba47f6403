const { DataTypes } = require("sequelize");
const { timestampFields } = require("../config/common-options");

/**
 * @type {import("sequelize").ModelAttributes}
 */
const attributes = {
  firstName: { type: DataTypes.STRING(255), allowNull: false },
  lastName: { type: DataTypes.STRING(255), allowNull: false },
  email: { type: DataTypes.STRING(255), allowNull: false, unique: true },
  password: { type: DataTypes.STRING(255), allowNull: false },
  role: { type: DataTypes.STRING(255) },
  status: { type: DataTypes.STRING(255) },
  workspace_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: "agency_workspaces",
      key: "id",
    },
  },
  email_verified_at: { type: DataTypes.DATE, allowNull: true },
  verification_token_expires_at: { type: DataTypes.DATE, allowNull: true },
  ...timestampFields,
};

module.exports = attributes;
