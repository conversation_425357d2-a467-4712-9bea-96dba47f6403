import { Request, Response, NextFunction } from "express";
import * as yup from "yup";
import * as httpStatus from "http-status";
import ApiError from "../utils/apiError";

/**
 * Pick specific keys from an object
 */
const pick = (object: Record<string, any>, keys: string[]): Record<string, any> => {
  return keys.reduce((obj: Record<string, any>, key: string) => {
    if (object && Object.prototype.hasOwnProperty.call(object, key)) {
      obj[key] = object[key];
    }
    return obj;
  }, {});
};

/**
 * Validation middleware factory
 * @param schema - Yup validation schema object with optional params, query, and body schemas
 * @returns Express middleware function
 */
const validate = (schema: {
  params?: yup.ObjectSchema<any>;
  query?: yup.ObjectSchema<any>;
  body?: yup.ObjectSchema<any>;
}) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const validSchema = pick(schema, ["params", "query", "body"]);
    const obj = pick(req, Object.keys(validSchema));

    try {
      const value = await yup.object(validSchema).validate(obj, { 
        abortEarly: false,
        stripUnknown: true,
      });

      // Filter the validated values to only include keys that were in the original request
      const filteredValue = Object.keys(value).reduce((acc: Record<string, any>, key: string) => {
        if (obj[key]) {
          acc[key] = Object.fromEntries(
            Object.entries(value[key]).filter(([subKey]) => 
              Object.prototype.hasOwnProperty.call(obj[key], subKey)
            )
          );
        }
        return acc;
      }, {});

      // Assign validated values back to request
      Object.assign(req, filteredValue);
      return next();
    } catch (error) {
      if (error instanceof yup.ValidationError) {
        const errorMessages = error.inner.map((err) => ({
          field: err.path,
          message: err.message,
        }));
        
        const errorMessage = errorMessages.map(err => err.message).join(", ");
        return next(new ApiError(httpStatus.BAD_REQUEST, errorMessage));
      }
      
      return next(new ApiError(httpStatus.BAD_REQUEST, "Validation error"));
    }
  };
};

export default validate;
