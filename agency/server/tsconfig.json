{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDirs": [".", "../../web"], "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declarationMap": false, "sourceMap": false, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "typeRoots": ["./node_modules/@types", "./types"], "baseUrl": "./", "paths": {"@/*": ["./*"], "web/*": ["../../web/*"]}, "allowJs": true}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "../../web/**/dist", "../../web/**/node_modules"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node", "transpileOnly": true, "compilerOptions": {"noImplicitAny": false, "strict": false}}}