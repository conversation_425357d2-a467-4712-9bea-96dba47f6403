/**
 * Example demonstrating how to import from the web folder
 * This shows the proper patterns for importing CommonJS modules from the web folder
 * into the agency server TypeScript files.
 */

// Example 1: Importing from workspace packages (storeseo-enums)
const cacheKeys = require("storeseo-enums/cacheKeys");
const toastMessages = require("storeseo-enums/toastMessages");

// Example 2: Importing from web folder through the database service
// This is the recommended approach for database models
const { AgencyUser, AgencyWorkspace, sequelize } = require("../services/database");

// Example 3: Type imports (for TypeScript type checking)
import type { Request, Response } from "express";

// Example 4: Direct import from web folder (use sparingly, prefer database service)
// const webUtility = require("web/api/utils/helper");

class WebImportExample {
  /**
   * Example method showing how to use imported web models
   */
  async getUserExample(userId: number) {
    try {
      // Using the imported AgencyUser model from web folder
      const user = await AgencyUser.findByPk(userId);
      
      if (!user) {
        throw new Error(toastMessages.USER_NOT_FOUND);
      }

      return {
        id: user.id,
        email: user.email,
        status: user.status
      };
    } catch (error) {
      console.error("Error fetching user:", error);
      throw error;
    }
  }

  /**
   * Example method showing database transactions
   */
  async createWorkspaceExample(name: string, ownerId: number) {
    const transaction = await sequelize.transaction();
    
    try {
      const workspace = await AgencyWorkspace.create({
        name,
        owner_id: ownerId,
        settings: {}
      }, { transaction });

      await transaction.commit();
      return workspace;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Example Express route handler using imported types and models
   */
  getUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const user = await this.getUserExample(parseInt(id));
      
      res.json({
        success: true,
        data: user
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  };
}

// Export using CommonJS pattern
module.exports = new WebImportExample();
