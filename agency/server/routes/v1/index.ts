import type { Router } from "express";
const express = require("express");
const demoRoute = require("./demo.route");
const authRoute = require("./auth.route");

const router = express.Router();

interface RouteConfig {
  path: string;
  route: Router;
}

const defaultRoutes: RouteConfig[] = [
  {
    path: "/demo",
    route: demoRoute,
  },
  {
    path: "/auth",
    route: authRoute,
  },
];

// TODO: API documentation with swagger
const devRoutes: RouteConfig[] = [
  // routes available only in development mode
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */
if (process.env.NODE_ENV === "development") {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

export = router;
