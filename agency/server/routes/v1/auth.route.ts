const passport = require("passport");
const express = require("express");
const Router = express.Router;
const validate = require("../../middlewares/validate");
const {
  registerSchema,
  verifyEmailSchema,
  setupWorkspaceSchema,
  refreshTokenSchema,
} = require("../../validations/auth");
const {
  register,
  verifyEmail,
  setupWorkspace,
  getProfile,
  logout,
  refreshToken,
} = require("../../controllers/auth.controller");

const router = Router();

/**
 * Authentication middleware for protected routes
 */
const auth = passport.authenticate("jwt", { session: false });

/**
 * Public routes (no authentication required)
 */

// User registration
router.post("/register", validate(registerSchema), register);

// Email verification
router.post("/verify-email", validate(verifyEmailSchema), verifyEmail);

// Token refresh
router.post("/refresh-token", validate(refreshTokenSchema), refreshToken);

/**
 * Protected routes (authentication required)
 */

// Workspace setup (requires verified user)
router.post("/setup-workspace", validate(setupWorkspaceSchema), setupWorkspace);

// Get current user profile
router.get("/me", auth, getProfile);

// Logout
router.post("/logout", auth, logout);

export = router;
