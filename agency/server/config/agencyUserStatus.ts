/**
 * Agency User Status Enum
 * Defines the possible status values for agency users throughout the authentication flow
 */
enum AgencyUserStatus {
  /**
   * User has registered but hasn't verified their email yet
   */
  VERIFICATION_PENDING = "VERIFICATION_PENDING",
  
  /**
   * User has verified their email and completed workspace setup
   */
  VERIFIED = "VERIFIED",
  
  /**
   * User account has been suspended by an administrator
   */
  SUSPENDED = "SUSPENDED",
  
  /**
   * User account has been marked for deletion
   */
  DELETED = "DELETED",
}

/**
 * Type definition for agency user status values
 */
type AgencyUserStatusType = typeof AgencyUserStatus[keyof typeof AgencyUserStatus];

/**
 * Helper function to check if a status is valid
 * @param status - The status to validate
 * @returns boolean indicating if the status is valid
 */
const isValidAgencyUserStatus = (status: string): status is AgencyUserStatusType => {
  return Object.values(AgencyUserStatus).includes(status as AgencyUserStatusType);
};

/**
 * Helper function to get all available status values
 * @returns Array of all status values
 */
const getAllAgencyUserStatuses = (): AgencyUserStatusType[] => {
  return Object.values(AgencyUserStatus);
};

/**
 * Helper function to get status display names
 * @param status - The status to get display name for
 * @returns Human-readable display name
 */
const getAgencyUserStatusDisplayName = (status: AgencyUserStatusType): string => {
  const displayNames: Record<AgencyUserStatusType, string> = {
    [AgencyUserStatus.VERIFICATION_PENDING]: "Verification Pending",
    [AgencyUserStatus.VERIFIED]: "Verified",
    [AgencyUserStatus.SUSPENDED]: "Suspended",
    [AgencyUserStatus.DELETED]: "Deleted",
  };
  
  return displayNames[status] || status;
};

export {
  AgencyUserStatus,
  AgencyUserStatusType,
  isValidAgencyUserStatus,
  getAllAgencyUserStatuses,
  getAgencyUserStatusDisplayName,
};

export default AgencyUserStatus;
