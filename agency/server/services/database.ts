/**
 * Database service to access web models from agency server
 * This service provides access to the main database models
 */

// Import the web database models
console.log("Loading web/sequelize...");
try {
  const db = require("web/sequelize");
  console.log("web/sequelize loaded successfully", Object.keys(db));
} catch (error) {
  console.error("Error loading web/sequelize:", error);
  throw error;
}
const db = require("web/sequelize");

// Export the models we need for agency authentication
module.exports = {
  AgencyUser: db.AgencyUser,
  AgencyWorkspace: db.AgencyWorkspace,
  sequelize: db.sequelize,
  ...db,
};
