import * as bcrypt from "bcrypt";
import * as jwt from "jsonwebtoken";
import { AgencyUserStatus } from "../config/agencyUserStatus";
import userTokenType from "../config/userTokenType";
import config from "../config/config";
import {
  RegisterUserData,
  RegisterUserResponse,
  VerificationResult,
  WorkspaceSetupResult,
  TokenPair,
  VerificationTokenPayload,
  AccessTokenPayload,
  RefreshTokenPayload,
} from "../types/auth";

class AuthService {
  /**
   * Register a new user (test implementation)
   */
  async registerUser(userData: RegisterUserData): Promise<RegisterUserResponse> {
    // Mock implementation for testing
    return {
      userId: 1,
      email: userData.email,
      verificationSent: true,
    };
  }

  /**
   * Verify email with token (test implementation)
   */
  async verifyEmail(token: string): Promise<VerificationResult> {
    // Mock implementation for testing
    return {
      userId: 1,
      email: "<EMAIL>",
      requiresWorkspaceSetup: true,
    };
  }

  /**
   * Setup workspace for verified user (test implementation)
   */
  async setupWorkspace(userId: number, workspaceName: string): Promise<WorkspaceSetupResult> {
    // Mock implementation for testing
    const tokens = await this.generateAccessTokens({
      id: userId,
      fullName: "Test User",
      email: "<EMAIL>",
      role: "OWNER",
      workspace_id: 1,
    });

    return {
      user: {
        id: userId,
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        role: "OWNER",
        status: AgencyUserStatus.VERIFIED,
      },
      workspace: {
        id: 1,
        name: workspaceName,
        slug: this.generateSlug(workspaceName),
      },
      tokens,
    };
  }

  /**
   * Generate verification token
   */
  async generateVerificationToken(user: any): Promise<string> {
    const payload: VerificationTokenPayload = {
      sub: user.id.toString(),
      email: user.email,
      link_type: "verification",
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (3 * 24 * 60 * 60), // 3 days
    };

    return jwt.sign(payload, config.jwt.secret);
  }

  /**
   * Generate access and refresh tokens
   */
  async generateAccessTokens(user: any): Promise<TokenPair> {
    const now = Math.floor(Date.now() / 1000);
    
    // Access token (5 minutes)
    const accessPayload: AccessTokenPayload = {
      sub: user.id.toString(),
      email: user.email,
      name: user.fullName,
      role: user.role,
      workspace_id: user.workspace_id,
      scopes: [], // TODO: Get user scopes based on role
      type: userTokenType.ACCESS,
      iat: now,
      exp: now + Math.floor(config.jwt.accessTokenExpirationDuration / 1000),
    };

    // Refresh token (7 days)
    const refreshPayload: RefreshTokenPayload = {
      sub: user.id.toString(),
      type: userTokenType.REFRESH,
      iat: now,
      exp: now + Math.floor(config.jwt.refreshTokenExpirationDuration / 1000),
    };

    const accessToken = jwt.sign(accessPayload, config.jwt.secret);
    const refreshToken = jwt.sign(refreshPayload, config.jwt.secret);

    return {
      access: {
        token: accessToken,
        expires: new Date((accessPayload.exp) * 1000).toISOString(),
      },
      refresh: {
        token: refreshToken,
        expires: new Date((refreshPayload.exp) * 1000).toISOString(),
      },
    };
  }

  /**
   * Generate workspace slug from name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }

  /**
   * Refresh access token (test implementation)
   */
  async refreshTokens(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.jwt.secret) as RefreshTokenPayload;
      
      if (decoded.type !== userTokenType.REFRESH) {
        throw new Error("Invalid token type");
      }

      // Mock user for testing
      const user = {
        id: decoded.sub,
        fullName: "Test User",
        email: "<EMAIL>",
        role: "OWNER",
        workspace_id: 1,
      };

      // Generate new tokens
      return await this.generateAccessTokens(user);
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Invalid or expired refresh token");
      }
      throw error;
    }
  }
}

export default new AuthService();
