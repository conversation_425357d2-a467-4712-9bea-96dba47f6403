const { AgencyWorkspace, sequelize } = require("./database");

class WorkspaceService {
  /**
   * Create a new workspace
   */
  async createWorkspace(name: string, ownerId: number): Promise<any> {
    const transaction = await sequelize.transaction();

    try {
      // Generate unique slug
      const slug = await this.generateSlug(name);

      // Create workspace
      const workspace = await AgencyWorkspace.create(
        {
          name,
          slug,
          owner_id: ownerId,
          settings: {},
        },
        { transaction }
      );

      await transaction.commit();
      return workspace;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Generate unique slug from workspace name
   */
  async generateSlug(name: string): Promise<string> {
    const baseSlug = this.createSlugFromName(name);
    let slug = baseSlug;
    let counter = 1;

    // Check for uniqueness and append number if needed
    while (await this.isSlugTaken(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Create slug from name
   */
  private createSlugFromName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-|-$/g, "") // Remove leading/trailing hyphens
      .substring(0, 50); // Limit length
  }

  /**
   * Check if slug is already taken
   */
  private async isSlugTaken(slug: string): Promise<boolean> {
    const existing = await AgencyWorkspace.findOne({
      where: { slug },
    });
    return !!existing;
  }

  /**
   * Validate workspace name
   */
  async validateWorkspaceName(name: string): Promise<boolean> {
    // Check length
    if (name.length < 3 || name.length > 50) {
      return false;
    }

    // Check for valid characters (alphanumeric, spaces, hyphens)
    const validPattern = /^[a-zA-Z0-9\s-]+$/;
    if (!validPattern.test(name)) {
      return false;
    }

    // Check that it's not just spaces or hyphens
    const trimmed = name.trim().replace(/-/g, "");
    if (trimmed.length === 0) {
      return false;
    }

    return true;
  }

  /**
   * Get workspace by ID
   */
  async getWorkspaceById(id: number): Promise<any> {
    return await AgencyWorkspace.findByPk(id, {
      include: [
        {
          association: "owner",
          attributes: ["id", "firstName", "lastName", "email"],
        },
        {
          association: "members",
          attributes: ["id", "firstName", "lastName", "email", "role", "status"],
        },
      ],
    });
  }

  /**
   * Get workspace by slug
   */
  async getWorkspaceBySlug(slug: string): Promise<any> {
    return await AgencyWorkspace.findOne({
      where: { slug },
      include: [
        {
          association: "owner",
          attributes: ["id", "firstName", "lastName", "email"],
        },
        {
          association: "members",
          attributes: ["id", "firstName", "lastName", "email", "role", "status"],
        },
      ],
    });
  }

  /**
   * Update workspace settings
   */
  async updateWorkspaceSettings(id: number, settings: Record<string, any>): Promise<any> {
    const workspace = await AgencyWorkspace.findByPk(id);
    if (!workspace) {
      throw new Error("Workspace not found");
    }

    const updatedSettings = {
      ...workspace.settings,
      ...settings,
    };

    await workspace.update({ settings: updatedSettings });
    return workspace;
  }

  /**
   * Get workspaces for user
   */
  async getWorkspacesForUser(userId: number): Promise<any[]> {
    return await AgencyWorkspace.findAll({
      where: { owner_id: userId },
      order: [["created_at", "DESC"]],
    });
  }

  /**
   * Delete workspace
   */
  async deleteWorkspace(id: number, ownerId: number): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      const workspace = await AgencyWorkspace.findByPk(id, { transaction });

      if (!workspace) {
        throw new Error("Workspace not found");
      }

      if (workspace.owner_id !== ownerId) {
        throw new Error("Not authorized to delete this workspace");
      }

      // TODO: Handle cleanup of associated data (users, etc.)
      await workspace.destroy({ transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

export default new WorkspaceService();
