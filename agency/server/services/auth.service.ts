const { AgencyUser } = require("./database");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const config = require("../config/config");
const { AgencyUserStatus } = require("../config/agencyUserStatus");
const userTokenType = require("../config/userTokenType");

interface RegisterUserData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

interface RegisterUserResponse {
  userId: number;
  email: string;
  verificationSent: boolean;
}

class AuthService {
  /**
   * Register a new user
   */
  async registerUser(userData: RegisterUserData): Promise<RegisterUserResponse> {
    try {
      // Check if user already exists
      const existingUser = await AgencyUser.findOne({
        where: { email: userData.email },
      });

      if (existingUser) {
        throw new Error("Email already exists");
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

      // Create user
      const user = await AgencyUser.create({
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        password: hashedPassword,
        status: AgencyUserStatus.VERIFICATION_PENDING,
      });

      console.log("user", user);

      return {
        userId: user.id,
        email: user.email,
        verificationSent: true,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Verify email with token
   */
  // async verifyEmail(token: string): Promise<VerificationResult> {
  //   try {
  //     // Verify and decode token
  //     const decoded = jwt.verify(token, config.jwt.secret) as VerificationTokenPayload;

  //     if (decoded.link_type !== "verification") {
  //       throw new Error("Invalid token type");
  //     }

  //     // Find user
  //     const user = await AgencyUser.findByPk(decoded.sub);
  //     if (!user) {
  //       throw new Error("User not found");
  //     }

  //     // Check if already verified
  //     if (user.isVerified) {
  //       throw new Error("Email already verified");
  //     }

  //     // Check if token is expired
  //     if (user.isVerificationExpired) {
  //       throw new Error("Verification token expired");
  //     }

  //     // Update user as verified
  //     await user.update({
  //       email_verified_at: new Date(),
  //       status: AgencyUserStatus.VERIFIED,
  //       verification_token_expires_at: null,
  //     });

  //     return {
  //       userId: user.id,
  //       email: user.email,
  //       requiresWorkspaceSetup: !user.workspace_id,
  //     };
  //   } catch (error) {
  //     if (error instanceof jwt.JsonWebTokenError) {
  //       throw new Error("Invalid or expired token");
  //     }
  //     throw error;
  //   }
  // }

  /**
   * Setup workspace for verified user
   */
  // async setupWorkspace(userId: number, workspaceName: string): Promise<WorkspaceSetupResult> {
  //   const transaction = await sequelize.transaction();

  //   try {
  //     // Find user
  //     const user = await AgencyUser.findByPk(userId, { transaction });
  //     if (!user) {
  //       throw new Error("User not found");
  //     }

  //     // Check if user is verified
  //     if (!user.isVerified) {
  //       throw new Error("Email not verified");
  //     }

  //     // Check if user already has a workspace
  //     if (user.workspace_id) {
  //       throw new Error("User already has a workspace");
  //     }

  //     // Generate workspace slug
  //     const slug = this.generateSlug(workspaceName);

  //     // Check if slug is unique
  //     const existingWorkspace = await AgencyWorkspace.findOne({
  //       where: { slug },
  //       transaction,
  //     });

  //     if (existingWorkspace) {
  //       throw new Error("Workspace name already taken");
  //     }

  //     // Create workspace
  //     const workspace = await AgencyWorkspace.create(
  //       {
  //         name: workspaceName,
  //         slug,
  //         owner_id: userId,
  //         settings: {},
  //       },
  //       { transaction }
  //     );

  //     // Update user with workspace
  //     await user.update(
  //       {
  //         workspace_id: workspace.id,
  //       },
  //       { transaction }
  //     );

  //     // Generate access tokens
  //     const tokens = await this.generateAccessTokens(user);

  //     await transaction.commit();

  //     return {
  //       user: {
  //         id: user.id,
  //         firstName: user.firstName,
  //         lastName: user.lastName,
  //         email: user.email,
  //         role: user.role,
  //         status: user.status,
  //       },
  //       workspace: {
  //         id: workspace.id,
  //         name: workspace.name,
  //         slug: workspace.slug,
  //       },
  //       tokens,
  //     };
  //   } catch (error) {
  //     await transaction.rollback();
  //     throw error;
  //   }
  // }

  /**
   * Generate verification token
   */
  // async generateVerificationToken(user: any): Promise<string> {
  //   const payload: VerificationTokenPayload = {
  //     sub: user.id.toString(),
  //     email: user.email,
  //     link_type: "verification",
  //     iat: Math.floor(Date.now() / 1000),
  //     exp: Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60, // 3 days
  //   };

  //   return jwt.sign(payload, config.jwt.secret);
  // }

  /**
   * Generate access and refresh tokens
   */
  // async generateAccessTokens(user: any): Promise<TokenPair> {
  //   const now = Math.floor(Date.now() / 1000);

  //   // Access token (5 minutes)
  //   const accessPayload: AccessTokenPayload = {
  //     sub: user.id.toString(),
  //     email: user.email,
  //     name: user.fullName,
  //     role: user.role,
  //     workspace_id: user.workspace_id,
  //     scopes: [], // TODO: Get user scopes based on role
  //     type: userTokenType.ACCESS,
  //     iat: now,
  //     exp: now + Math.floor(config.jwt.accessTokenExpirationDuration / 1000),
  //   };

  //   // Refresh token (7 days)
  //   const refreshPayload: RefreshTokenPayload = {
  //     sub: user.id.toString(),
  //     type: userTokenType.REFRESH,
  //     iat: now,
  //     exp: now + Math.floor(config.jwt.refreshTokenExpirationDuration / 1000),
  //   };

  //   const accessToken = jwt.sign(accessPayload, config.jwt.secret);
  //   const refreshToken = jwt.sign(refreshPayload, config.jwt.secret);

  //   return {
  //     access: {
  //       token: accessToken,
  //       expires: new Date(accessPayload.exp * 1000).toISOString(),
  //     },
  //     refresh: {
  //       token: refreshToken,
  //       expires: new Date(refreshPayload.exp * 1000).toISOString(),
  //     },
  //   };
  // }

  /**
   * Generate workspace slug from name
   */
  // private generateSlug(name: string): string {
  //   return name
  //     .toLowerCase()
  //     .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
  //     .replace(/\s+/g, "-") // Replace spaces with hyphens
  //     .replace(/-+/g, "-") // Replace multiple hyphens with single
  //     .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  // }

  /**
   * Refresh access token
   */
  // async refreshTokens(refreshToken: string): Promise<TokenPair> {
  //   try {
  //     // Verify refresh token
  //     const decoded = jwt.verify(refreshToken, config.jwt.secret) as RefreshTokenPayload;

  //     if (decoded.type !== userTokenType.REFRESH) {
  //       throw new Error("Invalid token type");
  //     }

  //     // Find user
  //     const user = await AgencyUser.findByPk(decoded.sub);
  //     if (!user) {
  //       throw new Error("User not found");
  //     }

  //     // Generate new tokens
  //     return await this.generateAccessTokens(user);
  //   } catch (error) {
  //     if (error instanceof jwt.JsonWebTokenError) {
  //       throw new Error("Invalid or expired refresh token");
  //     }
  //     throw error;
  //   }
  // }
}

export = new AuthService();
