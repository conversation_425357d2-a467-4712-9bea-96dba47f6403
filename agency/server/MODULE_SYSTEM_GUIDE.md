# Module System Guide

This guide documents the standardized module system for the StoreSEO project, covering imports/exports between the agency server and web folder.

## Overview

The project uses **CommonJS** as the unified module system to ensure compatibility between:
- **Agency Server** (TypeScript with CommonJS compilation)
- **Web Folder** (JavaScript with CommonJS)

## Configuration

### Agency Server Configuration

**tsconfig.json:**
```json
{
  "compilerOptions": {
    "module": "commonjs",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "paths": {
      "@/*": ["./*"],
      "web/*": ["../../web/*"]
    }
  }
}
```

**package.json:**
```json
{
  "type": "commonjs"
}
```

### Web Folder Configuration

**package.json:**
```json
{
  // No "type" field = defaults to CommonJS
}
```

## Import/Export Patterns

### 1. Agency Server Files (TypeScript)

**✅ Correct Patterns:**

```typescript
// Runtime imports - use require()
const express = require("express");
const httpStatus = require("http-status");
const config = require("./config/config");

// Type-only imports - use import type
import type { Request, Response } from "express";

// Exports - use module.exports or export =
module.exports = new AuthController();
// OR
export = router;
```

**❌ Avoid These Patterns:**

```typescript
// Don't mix ES imports with CommonJS in the same file
import express from "express"; // ❌
const config = require("./config"); // ❌

// Don't use ES exports
export default controller; // ❌
export { router }; // ❌
```

### 2. Web Folder Files (JavaScript)

**✅ Correct Patterns:**

```javascript
// Imports
const { Model } = require("sequelize");
const logger = require("storeseo-logger");

// Exports
module.exports = new BetterDocsService();
// OR
module.exports = (sequelize, DataTypes) => {
  // model definition
};
```

## Cross-Folder Imports

### Importing from Web Folder into Agency Server

**Recommended: Use Database Service**

```typescript
// agency/server/services/database.ts
const db = require("web/sequelize");

module.exports = {
  AgencyUser: db.AgencyUser,
  AgencyWorkspace: db.AgencyWorkspace,
  sequelize: db.sequelize,
  ...db
};
```

```typescript
// In your agency server files
const { AgencyUser, sequelize } = require("./services/database");
```

**Direct Import (use sparingly):**

```typescript
// Only for utilities that don't require initialization
const webUtility = require("web/api/utils/helper");
```

### Importing Workspace Packages

```typescript
// Both agency server and web folder
const cacheKeys = require("storeseo-enums/cacheKeys");
const toastMessages = require("storeseo-enums/toastMessages");
```

## File Structure Examples

### Agency Server Controller

```typescript
// agency/server/controllers/auth.controller.ts
import type { Request, Response } from "express";
const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");
const AuthService = require("../services/auth.service");

class AuthController {
  register = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const result = await AuthService.registerUser(req.body);
    res.status(httpStatus.CREATED).json(result);
  });
}

module.exports = new AuthController();
```

### Agency Server Route

```typescript
// agency/server/routes/v1/auth.route.ts
const express = require("express");
const validate = require("../../middlewares/validate");
const authController = require("../../controllers/auth.controller");

const router = express.Router();

router.post("/register", validate(registerSchema), authController.register);

export = router;
```

### Agency Server Service

```typescript
// agency/server/services/auth.service.ts
import type { RegisterUserData } from "@/types/auth";
const bcrypt = require("bcrypt");
const { AgencyUser } = require("./database");

class AuthService {
  async registerUser(userData: RegisterUserData) {
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    return await AgencyUser.create({
      ...userData,
      password: hashedPassword
    });
  }
}

module.exports = new AuthService();
```

## Best Practices

### 1. Consistency
- Use `require()` for all runtime imports in agency server
- Use `import type` only for TypeScript types
- Use `module.exports` or `export =` for exports

### 2. Web Folder Access
- Prefer the database service for model access
- Avoid direct imports from web folder when possible
- Use path mapping: `web/*` resolves to `../../web/*`

### 3. Error Handling
- Web folder imports may fail if environment variables are missing
- Always handle import errors gracefully
- Test imports in isolation when possible

## Testing

Run the module system test:

```bash
cd agency/server
npx ts-node test-imports.js
```

This verifies:
- ✅ CommonJS require() statements work
- ✅ TypeScript CommonJS compilation works  
- ✅ Workspace package imports work
- ✅ Cross-folder path resolution works
- ✅ Module exports/imports are consistent

## Troubleshooting

### Common Issues

1. **"Cannot find module" errors**
   - Check tsconfig.json path mappings
   - Verify file extensions (.ts vs .js)
   - Ensure files exist at expected paths

2. **Environment variable errors from web folder**
   - Web folder requires proper .env setup
   - Use database service instead of direct imports
   - Test imports in isolation

3. **TypeScript compilation errors**
   - Ensure `"module": "commonjs"` in tsconfig.json
   - Use `import type` for type-only imports
   - Use `export =` for CommonJS exports

### Migration Checklist

When converting files to CommonJS:

- [ ] Replace `import` with `require()` for runtime imports
- [ ] Keep `import type` for TypeScript types
- [ ] Replace `export default` with `module.exports`
- [ ] Replace `export { }` with `module.exports = { }`
- [ ] Update Router imports: `const express = require("express")`
- [ ] Test compilation with `npx ts-node filename.ts`
