import { AgencyUserStatusType } from "@/config/agencyUserStatus";

/**
 * User registration request data
 */
export interface RegisterUserData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

/**
 * User registration response data
 */
export interface RegisterUserResponse {
  userId: number;
  email: string;
  verificationSent: boolean;
}

/**
 * Email verification request data
 */
export interface VerifyEmailData {
  token: string;
}

/**
 * Email verification response data
 */
export interface VerificationResult {
  userId: number;
  email: string;
  requiresWorkspaceSetup: boolean;
}

/**
 * Workspace setup request data
 */
export interface WorkspaceSetupData {
  workspaceName: string;
}

/**
 * Workspace setup response data
 */
export interface WorkspaceSetupResult {
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
    status: AgencyUserStatusType;
  };
  workspace: {
    id: number;
    name: string;
    slug: string;
  };
  tokens: TokenPair;
}

/**
 * JWT token pair (access + refresh)
 */
export interface TokenPair {
  access: {
    token: string;
    expires: string;
  };
  refresh: {
    token: string;
    expires: string;
  };
}

/**
 * Verification token payload
 */
export interface VerificationTokenPayload {
  sub: string; // User ID
  email: string; // User email
  link_type: "verification";
  iat: number; // Issued at
  exp: number; // Expires at (3 days)
}

/**
 * Access token payload
 */
export interface AccessTokenPayload {
  sub: string; // User ID
  email: string; // User email
  name: string; // Full name
  role: string; // User role
  workspace_id: number; // Workspace ID
  scopes: string[]; // Permission scopes
  type: "access";
  iat: number; // Issued at
  exp: number; // Expires at (5 minutes)
}

/**
 * Refresh token payload
 */
export interface RefreshTokenPayload {
  sub: string; // User ID
  type: "refresh";
  iat: number; // Issued at
  exp: number; // Expires at (7 days)
}

/**
 * Database user model interface
 */
export interface AgencyUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: string;
  status: AgencyUserStatusType;
  workspace_id?: number;
  email_verified_at?: Date;
  verification_token_expires_at?: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Database workspace model interface
 */
export interface AgencyWorkspace {
  id: number;
  name: string;
  slug: string;
  owner_id: number;
  settings: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}
