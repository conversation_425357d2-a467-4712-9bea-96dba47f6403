import type { Request, Response } from "express";
const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");

interface User {
  id: number;
  name: string;
  email: string;
}

interface Stats {
  totalUsers: number;
  activeUsers: number;
  lastUpdated: string;
}

interface DemoData {
  message: string;
  timestamp: string;
  version: string;
  environment: string;
  data: {
    users: User[];
    stats: Stats;
  };
}

interface HealthData {
  status: string;
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  version: string;
}

const getDemoData = catchAsync(async (req: Request, res: Response): Promise<void> => {
  const demoData: DemoData = {
    message: "Hello from Agency Server API!",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    data: {
      users: [
        { id: 1, name: "<PERSON>", email: "<EMAIL>" },
        { id: 2, name: "<PERSON>", email: "<EMAIL>" },
      ],
      stats: {
        totalUsers: 2,
        activeUsers: 1,
        lastUpdated: new Date().toISOString(),
      },
    },
  };

  res.status(httpStatus.OK).json(demoData);
});

const getHealthCheck = catchAsync(async (req: Request, res: Response): Promise<void> => {
  const healthData: HealthData = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: "1.0.0",
  };

  res.status(httpStatus.OK).json(healthData);
});

export = {
  getDemoData,
  getHealthCheck,
};
