const catchAsync = require("../utils/catchAsync");
const httpStatus = require("http-status");
const ApiError = require("../utils/apiError");
const AuthService = require("../services/auth.service");
const { RegisterUserData, VerifyEmailData, WorkspaceSetupData } = require("../types/auth");
import type { Request, Response } from "express";

class AuthController {
  /**
   * Register a new user
   * POST /v1/auth/register
   */
  register = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const userData = req.body;

    try {
      const result = await AuthService.registerUser(userData);

      // Generate verification token
      const verificationToken = await AuthService.generateVerificationToken({
        id: result.userId,
        email: result.email,
      });

      // TODO: Send verification email
      console.log(`Verification token for ${result.email}: ${verificationToken}`);

      res.status(httpStatus.CREATED).json({
        success: true,
        message: "Registration successful. Please check your email for verification.",
        data: result,
      });
    } catch (error: any) {
      if (error.message === "Email already exists") {
        throw new ApiError(httpStatus.CONFLICT, "Email already exists");
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
    }
  });
}

/**
 * Verify email with token
 * POST /v1/auth/verify-email
 */
// const verifyEmail = catchAsync(async (req: Request, res: Response): Promise<void> => {
//   const { token }: VerifyEmailData = req.body;

//   try {
//     const result = await authService.verifyEmail(token);

//     res.status(httpStatus.OK).json({
//       success: true,
//       message: "Email verified successfully",
//       data: result,
//     });
//   } catch (error: any) {
//     if (error.message.includes("Invalid") || error.message.includes("expired")) {
//       throw new ApiError(httpStatus.BAD_REQUEST, error.message);
//     }
//     if (error.message === "User not found") {
//       throw new ApiError(httpStatus.NOT_FOUND, "User not found");
//     }
//     if (error.message === "Email already verified") {
//       throw new ApiError(httpStatus.BAD_REQUEST, "Email already verified");
//     }
//     throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
//   }
// });

/**
 * Setup workspace for verified user
 * POST /v1/auth/setup-workspace
 */
// const setupWorkspace = catchAsync(async (req: Request, res: Response): Promise<void> => {
//   const { workspaceName }: WorkspaceSetupData = req.body;

//   // Extract user ID from JWT token (this will be set by auth middleware)
//   const userId = parseInt((req as any).user?.id || req.body.userId);

//   if (!userId) {
//     throw new ApiError(httpStatus.UNAUTHORIZED, "User authentication required");
//   }

//   try {
//     const result = await authService.setupWorkspace(userId, workspaceName);

//     res.status(httpStatus.CREATED).json({
//       success: true,
//       message: "Workspace created successfully",
//       data: result,
//     });
//   } catch (error: any) {
//     if (error.message === "User not found") {
//       throw new ApiError(httpStatus.NOT_FOUND, "User not found");
//     }
//     if (error.message === "Email not verified") {
//       throw new ApiError(httpStatus.BAD_REQUEST, "Email not verified");
//     }
//     if (error.message === "User already has a workspace") {
//       throw new ApiError(httpStatus.BAD_REQUEST, "User already has a workspace");
//     }
//     if (error.message === "Workspace name already taken") {
//       throw new ApiError(httpStatus.CONFLICT, "Workspace name already taken");
//     }
//     throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
//   }
// });

/**
 * Refresh access token
 * POST /v1/auth/refresh-token
 */
// const refreshToken = catchAsync(async (req: Request, res: Response): Promise<void> => {
//   const { refreshToken } = req.body;

//   if (!refreshToken) {
//     throw new ApiError(httpStatus.BAD_REQUEST, "Refresh token is required");
//   }

//   try {
//     const tokens = await authService.refreshTokens(refreshToken);

//     res.status(httpStatus.OK).json({
//       success: true,
//       message: "Tokens refreshed successfully",
//       data: { tokens },
//     });
//   } catch (error: any) {
//     if (error.message.includes("Invalid") || error.message.includes("expired")) {
//       throw new ApiError(httpStatus.UNAUTHORIZED, error.message);
//     }
//     if (error.message === "User not found") {
//       throw new ApiError(httpStatus.NOT_FOUND, "User not found");
//     }
//     throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
//   }
// });

/**
 * Get current user profile
 * GET /v1/auth/me
 */
// const getProfile = catchAsync(async (req: Request, res: Response): Promise<void> => {
//   const user = (req as any).user;

//   if (!user) {
//     throw new ApiError(httpStatus.UNAUTHORIZED, "User not authenticated");
//   }

//   res.status(httpStatus.OK).json({
//     success: true,
//     message: "User profile retrieved successfully",
//     data: {
//       user: {
//         id: user.id,
//         email: user.email,
//         name: user.name,
//         role: user.role,
//         scopes: user.scopes,
//       },
//     },
//   });
// });

/**
 * Logout user (for future use - mainly for token blacklisting)
 * POST /v1/auth/logout
 */
// const logout = catchAsync(async (req: Request, res: Response): Promise<void> => {
//   // TODO: Implement token blacklisting if needed

//   res.status(httpStatus.OK).json({
//     success: true,
//     message: "Logged out successfully",
//   });
// });

// export { getProfile, logout, refreshToken, register, setupWorkspace, verifyEmail };
module.exports = new AuthController();
