import * as yup from "yup";

/**
 * User registration validation schema
 */
export const registerSchema = {
  body: yup.object({
    firstName: yup
      .string()
      .required("First name is required")
      .min(2, "First name must be at least 2 characters")
      .max(50, "First name must not exceed 50 characters")
      .matches(/^[a-zA-Z\s]+$/, "First name can only contain letters and spaces"),
    
    lastName: yup
      .string()
      .required("Last name is required")
      .min(2, "Last name must be at least 2 characters")
      .max(50, "Last name must not exceed 50 characters")
      .matches(/^[a-zA-Z\s]+$/, "Last name can only contain letters and spaces"),
    
    email: yup
      .string()
      .required("Email is required")
      .email("Please enter a valid email address")
      .max(255, "Email must not exceed 255 characters"),
    
    password: yup
      .string()
      .required("Password is required")
      .min(8, "Password must be at least 8 characters")
      .max(255, "Password must not exceed 255 characters")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number"
      ),
  }),
};

/**
 * Email verification validation schema
 */
export const verifyEmailSchema = {
  body: yup.object({
    token: yup
      .string()
      .required("Verification token is required")
      .min(1, "Token cannot be empty"),
  }),
};

/**
 * Workspace setup validation schema
 */
export const setupWorkspaceSchema = {
  body: yup.object({
    workspaceName: yup
      .string()
      .required("Workspace name is required")
      .min(3, "Workspace name must be at least 3 characters")
      .max(50, "Workspace name must not exceed 50 characters")
      .matches(
        /^[a-zA-Z0-9\s-]+$/,
        "Workspace name can only contain letters, numbers, spaces, and hyphens"
      )
      .test(
        "not-only-spaces-hyphens",
        "Workspace name cannot contain only spaces and hyphens",
        (value) => {
          if (!value) return false;
          const trimmed = value.trim().replace(/-/g, '');
          return trimmed.length > 0;
        }
      ),
  }),
};

/**
 * Token refresh validation schema
 */
export const refreshTokenSchema = {
  body: yup.object({
    refreshToken: yup
      .string()
      .required("Refresh token is required")
      .min(1, "Refresh token cannot be empty"),
  }),
};

/**
 * Login validation schema (for future use)
 */
export const loginSchema = {
  body: yup.object({
    email: yup
      .string()
      .required("Email is required")
      .email("Please enter a valid email address"),
    
    password: yup
      .string()
      .required("Password is required")
      .min(1, "Password cannot be empty"),
  }),
};

/**
 * Password reset request validation schema (for future use)
 */
export const passwordResetRequestSchema = {
  body: yup.object({
    email: yup
      .string()
      .required("Email is required")
      .email("Please enter a valid email address"),
  }),
};

/**
 * Password reset validation schema (for future use)
 */
export const passwordResetSchema = {
  body: yup.object({
    token: yup
      .string()
      .required("Reset token is required")
      .min(1, "Token cannot be empty"),
    
    password: yup
      .string()
      .required("Password is required")
      .min(8, "Password must be at least 8 characters")
      .max(255, "Password must not exceed 255 characters")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number"
      ),
    
    confirmPassword: yup
      .string()
      .required("Password confirmation is required")
      .oneOf([yup.ref('password')], "Passwords must match"),
  }),
};
